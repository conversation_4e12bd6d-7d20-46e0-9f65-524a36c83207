{"name": "gac-info", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "tauri": "tauri"}, "dependencies": {"@mantine/charts": "^8.1.3", "@mantine/core": "^8.1.3", "@mantine/hooks": "^8.1.3", "@mantine/modals": "^8.1.3", "@tailwindcss/vite": "^4.1.11", "@tauri-apps/api": "^2", "@tauri-apps/plugin-http": "^2.5.0", "@tauri-apps/plugin-opener": "^2", "react": "^18.3.1", "react-dom": "^18.3.1", "recharts": "2", "tailwindcss": "^4.1.11"}, "devDependencies": {"@tauri-apps/cli": "^2", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "typescript": "~5.6.2", "vite": "^6.0.3"}}