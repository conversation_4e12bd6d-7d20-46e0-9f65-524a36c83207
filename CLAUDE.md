# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Tauri desktop application built with React, TypeScript, and Rust. The project combines a React frontend with a Rust backend for cross-platform desktop application development.

## Key Technologies

- **Frontend**: React 18 + TypeScript + Vite
- **UI Framework**: <PERSON><PERSON> (UI components) + Tailwind CSS (styling)
- **Desktop Framework**: Tauri 2.0 (Rust backend)
- **Package Manager**: pnpm
- **Build Tools**: Vite (frontend), Cargo (Rust backend)

## Project Structure

```
/
├── src/                    # React frontend source
│   ├── App.tsx            # Main React component
│   ├── main.tsx           # React entry point
│   └── assets/            # Static assets
├── src-tauri/             # Rust backend source
│   ├── src/
│   │   ├── main.rs        # Rust entry point
│   │   └── lib.rs         # Tauri commands and app setup
│   ├── Cargo.toml         # Rust dependencies
│   └── tauri.conf.json    # Tauri configuration
├── public/                # Static web assets
├── package.json           # Node.js dependencies and scripts
└── vite.config.ts         # Vite configuration
```

## Development Commands

### Frontend Development
- `pnpm dev` - Start development server (frontend only)
- `pnpm build` - Build frontend for production
- `pnpm preview` - Preview production build

### Tauri Development
- `pnpm tauri dev` - Start Tauri development mode (full app)
- `pnpm tauri build` - Build desktop application for production

### Build Process
- TypeScript compilation: `tsc`
- Frontend build: `vite build`
- Complete build: `pnpm build` (runs both TypeScript and Vite)

## Architecture Notes

### Frontend-Backend Communication
- Uses Tauri's `invoke` API to call Rust functions from React
- Example: `invoke("greet", { name })` calls the `greet` command in Rust
- Rust commands are defined with `#[tauri::command]` attribute

### UI Components
- Mantine components wrapped in `MantineProvider`
- Tailwind CSS for utility styling
- Current App component is minimal - main content area is empty

### Rust Backend
- Main logic in `src-tauri/src/lib.rs`
- Commands registered in `tauri::Builder` with `generate_handler!`
- Uses `tauri-plugin-opener` for system integration

## Configuration

### Development Server
- Frontend runs on port 1420 (configured in vite.config.ts)
- HMR on port 1421
- Tauri expects fixed ports (strictPort: true)

### Build Configuration
- Tauri config in `src-tauri/tauri.conf.json`
- Frontend dist output to `../dist`
- App window: 800x600px default size