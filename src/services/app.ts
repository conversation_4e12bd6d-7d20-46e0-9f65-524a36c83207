import { invoke } from "@tauri-apps/api/core";
import { ApiService } from "./api";
import { StorageService } from "./storage";
import { TrayService } from "./tray";
import type { AppState, GacBalanceResponse } from "../types";

export class AppService {
  private static state: AppState = {
    balance: null,
    token: null,
    isPolling: false,
    lastUpdate: null,
    error: null,
  };

  private static pollInterval: number | null = null;
  private static readonly POLL_INTERVAL_MS = 60 * 1000; // 1分钟

  private static listeners: Array<(state: AppState) => void> = [];

  static async initialize(): Promise<void> {
    try {
      // 初始化托盘
      await TrayService.initialize();

      // 加载保存的 token
      const savedToken = StorageService.getToken();
      if (savedToken) {
        this.state.token = savedToken;
        // 立即获取一次余额
        await this.fetchBalance();
        // 开始轮询
        this.startPolling();
      }

      // 监听刷新事件
      window.addEventListener("refresh-balance", () => {
        this.fetchBalance();
      });

      console.log("App service initialized successfully");
    } catch (error) {
      console.error("Failed to initialize app service:", error);
      this.updateState({ error: `初始化失败: ${error}` });
    }
  }

  static getState(): AppState {
    return { ...this.state };
  }

  static subscribe(listener: (state: AppState) => void): () => void {
    this.listeners.push(listener);
    // 立即调用一次
    listener(this.getState());

    // 返回取消订阅函数
    return () => {
      const index = this.listeners.indexOf(listener);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  static async setToken(token: string): Promise<void> {
    try {
      this.updateState({ token, error: null });
      StorageService.saveToken(token);

      // 立即获取余额
      await this.fetchBalance();

      // 开始轮询
      this.startPolling();
    } catch (error) {
      console.error("Failed to set token:", error);
      this.updateState({ error: `设置 Token 失败: ${error}` });
    }
  }

  static async removeToken(): Promise<void> {
    this.stopPolling();
    StorageService.removeToken();
    this.updateState({
      token: null,
      balance: null,
      lastUpdate: null,
      error: null,
    });
    await TrayService.updateBalance(null);
  }

  static async fetchBalance(): Promise<void> {
    if (!this.state.token) {
      this.updateState({ error: "Token 未设置" });
      return;
    }

    try {
      const response: GacBalanceResponse = await ApiService.getBalance(
        this.state.token
      );

      this.updateState({
        balance: response.balance,
        lastUpdate: new Date(),
        error: null,
      });

      await TrayService.updateBalance(response.balance);

      console.log("Balance updated:", response.balance);
    } catch (error) {
      console.error("Failed to fetch balance:", error);
      const errorMessage = `获取余额失败: ${error}`;
      this.updateState({ error: errorMessage });
      await TrayService.updateError(errorMessage);
    }
  }

  private static async checkScreenState(): Promise<boolean> {
    try {
      const isActive = await invoke<boolean>("is_screen_active");
      return isActive;
    } catch (error) {
      console.error("Failed to check screen state:", error);
      return true; // Default to active if check fails
    }
  }

  private static startPolling(): void {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
    }

    this.pollInterval = window.setInterval(async () => {
      // Check screen state before fetching balance
      const isScreenActive = await this.checkScreenState();
      if (isScreenActive) {
        this.fetchBalance();
      } else {
        console.log("Screen is not active, skipping balance fetch");
      }
    }, this.POLL_INTERVAL_MS);

    this.updateState({ isPolling: true });
    console.log("Polling started with screen state detection");
  }

  private static stopPolling(): void {
    if (this.pollInterval) {
      clearInterval(this.pollInterval);
      this.pollInterval = null;
    }

    this.updateState({ isPolling: false });
    console.log("Polling stopped");
  }

  private static updateState(updates: Partial<AppState>): void {
    this.state = { ...this.state, ...updates };

    // 通知所有监听器
    this.listeners.forEach((listener) => {
      try {
        listener(this.getState());
      } catch (error) {
        console.error("Error in state listener:", error);
      }
    });
  }

  static async destroy(): Promise<void> {
    this.stopPolling();
    await TrayService.destroy();
    this.listeners.length = 0;
  }
}
