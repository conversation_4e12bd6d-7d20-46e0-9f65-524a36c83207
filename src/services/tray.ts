import { TrayIcon } from "@tauri-apps/api/tray";
import { Menu } from "@tauri-apps/api/menu";
import { getCurrentWebviewWindow } from "@tauri-apps/api/webviewWindow";

export class TrayService {
  private static trayIcon: TrayIcon | null = null;

  static async initialize(): Promise<void> {
    try {
      // 创建托盘菜单
      const menu = await Menu.new({
        items: [
          {
            id: "show",
            text: "显示窗口",
            action: () => this.showWindow(),
          },
          {
            id: "refresh",
            text: "刷新余额",
            action: () => this.refreshBalance(),
          },
          {
            id: "separator1",
            text: "",
          },
          {
            id: "quit",
            text: "退出",
            action: () => this.quit(),
          },
        ],
      });

      // 创建托盘图标
      this.trayIcon = await TrayIcon.new({
        id: "gac-info-tray",
        tooltip: "GAC Info - 点击查看余额",
        menu,
        action: (event) => {
          if (event.type === "Click") {
            this.showWindow();
          }
        },
      });

      console.log("Tray icon initialized successfully");
    } catch (error) {
      console.error("Failed to initialize tray icon:", error);
      throw error;
    }
  }

  static async updateBalance(balance: number | null): Promise<void> {
    if (!this.trayIcon) return;

    try {
      const balanceText = balance !== null ? balance.toString() : "?";
      await this.trayIcon.setTooltip(`GAC Info - 余额: ${balanceText}`);

      // 这里可以生成带数字的图标，暂时先用文字提示
      console.log(`Tray updated with balance: ${balanceText}`);
    } catch (error) {
      console.error("Failed to update tray balance:", error);
    }
  }

  static async updateError(error: string): Promise<void> {
    if (!this.trayIcon) return;

    try {
      await this.trayIcon.setTooltip(`GAC Info - 错误: ${error}`);
    } catch (error) {
      console.error("Failed to update tray error:", error);
    }
  }

  private static async showWindow(): Promise<void> {
    try {
      const window = getCurrentWebviewWindow();
      await window.show();
      await window.setFocus();
    } catch (error) {
      console.error("Failed to show window:", error);
    }
  }

  private static refreshBalance(): void {
    // 触发余额刷新事件
    window.dispatchEvent(new CustomEvent("refresh-balance"));
  }

  private static async quit(): Promise<void> {
    try {
      const { exit } = await import("@tauri-apps/plugin-process");
      await exit(0);
    } catch (error) {
      console.error("Failed to quit application:", error);
    }
  }

  static async destroy(): Promise<void> {
    if (this.trayIcon) {
      try {
        await this.trayIcon.close();
        this.trayIcon = null;
      } catch (error) {
        console.error("Failed to destroy tray icon:", error);
      }
    }
  }
}
