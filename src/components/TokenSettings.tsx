import { useState } from 'react';
import { 
  TextInput, 
  Button, 
  Stack, 
  Text, 
  Alert,
  Group,
  ActionIcon,
  Tooltip
} from '@mantine/core';
import { IconKey, IconTrash, IconRefresh } from '@tabler/icons-react';
import { AppService } from '../services/app';
import type { AppState } from '../types';

interface TokenSettingsProps {
  state: AppState;
}

export function TokenSettings({ state }: TokenSettingsProps) {
  const [tokenInput, setTokenInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleSetToken = async () => {
    if (!tokenInput.trim()) return;

    setIsLoading(true);
    try {
      await AppService.setToken(tokenInput.trim());
      setTokenInput('');
    } catch (error) {
      console.error('Failed to set token:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRemoveToken = async () => {
    await AppService.removeToken();
  };

  const handleRefresh = async () => {
    setIsLoading(true);
    try {
      await AppService.fetchBalance();
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Stack gap="md">
      <Text size="lg" fw={600}>Token 设置</Text>
      
      {state.token ? (
        <Stack gap="sm">
          <Group justify="space-between">
            <Text size="sm" c="dimmed">
              Token 已设置 (***{state.token.slice(-8)})
            </Text>
            <Group gap="xs">
              <Tooltip label="刷新余额">
                <ActionIcon 
                  variant="light" 
                  onClick={handleRefresh}
                  loading={isLoading}
                >
                  <IconRefresh size={16} />
                </ActionIcon>
              </Tooltip>
              <Tooltip label="删除 Token">
                <ActionIcon 
                  variant="light" 
                  color="red" 
                  onClick={handleRemoveToken}
                >
                  <IconTrash size={16} />
                </ActionIcon>
              </Tooltip>
            </Group>
          </Group>
          
          {state.balance !== null && (
            <Alert color="green" title="当前余额">
              {state.balance} credits
            </Alert>
          )}
          
          {state.lastUpdate && (
            <Text size="xs" c="dimmed">
              最后更新: {state.lastUpdate.toLocaleString()}
            </Text>
          )}
          
          {state.isPolling && (
            <Text size="xs" c="blue">
              ✓ 自动轮询已启用 (每分钟更新)
            </Text>
          )}
        </Stack>
      ) : (
        <Stack gap="sm">
          <TextInput
            label="Authorization Token"
            placeholder="请输入 Bearer Token"
            value={tokenInput}
            onChange={(e) => setTokenInput(e.target.value)}
            leftSection={<IconKey size={16} />}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                handleSetToken();
              }
            }}
          />
          <Button 
            onClick={handleSetToken}
            disabled={!tokenInput.trim()}
            loading={isLoading}
          >
            设置 Token
          </Button>
        </Stack>
      )}
      
      {state.error && (
        <Alert color="red" title="错误">
          {state.error}
        </Alert>
      )}
    </Stack>
  );
}
