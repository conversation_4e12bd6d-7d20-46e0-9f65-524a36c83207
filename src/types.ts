// API 响应类型
export interface GacBalanceResponse {
  balance: number;
  creditCap: number;
  refillRate: number;
  lastRefill: string;
}

// 应用状态类型
export interface AppState {
  balance: number | null;
  token: string | null;
  isPolling: boolean;
  lastUpdate: Date | null;
  error: string | null;
}

// 配置类型
export interface AppConfig {
  apiUrl: string;
  pollInterval: number; // 毫秒
  tokenKey: string;
}
