import { useState, useEffect } from "react";
import { <PERSON><PERSON>Provider, Container, Stack, Title, Text } from "@mantine/core";
import { AppService } from "./services/app";
import { TokenSettings } from "./components/TokenSettings";
import type { AppState } from "./types";
import "./App.css";
import "@mantine/core/styles.css";

function App() {
  const [appState, setAppState] = useState<AppState>({
    balance: null,
    token: null,
    isPolling: false,
    lastUpdate: null,
    error: null,
  });

  useEffect(() => {
    // 初始化应用服务
    AppService.initialize().catch(console.error);

    // 订阅状态变化
    const unsubscribe = AppService.subscribe(setAppState);

    // 清理函数
    return () => {
      unsubscribe();
      AppService.destroy().catch(console.error);
    };
  }, []);

  return (
    <MantineProvider>
      <Container size="sm" py="md">
        <Stack gap="lg">
          <Stack gap="xs" align="center">
            <Title order={2}>GAC Info</Title>
            <Text size="sm" c="dimmed">
              GAC 余额监控工具
            </Text>
          </Stack>

          <TokenSettings state={appState} />
        </Stack>
      </Container>
    </MantineProvider>
  );
}

export default App;
