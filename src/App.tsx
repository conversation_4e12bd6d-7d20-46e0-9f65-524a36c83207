import { useState } from "react";
import reactLogo from "./assets/react.svg";
import { invoke } from "@tauri-apps/api/core";
import { MantineProvider } from "@mantine/core";
import "./App.css";
import "@mantine/core/styles.css";

function App() {
  const [greetMsg, setGreetMsg] = useState("");
  const [name, setName] = useState("");

  async function greet() {
    // Learn more about Tauri commands at https://tauri.app/develop/calling-rust/
    setGreetMsg(await invoke("greet", { name }));
  }

  return (
    <MantineProvider>
      <main className="h-full w-full"></main>
    </MantineProvider>
  );
}

export default App;
