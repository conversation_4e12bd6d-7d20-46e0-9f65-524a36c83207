{"$schema": "https://schema.tauri.app/config/2", "productName": "gac-info", "version": "0.1.0", "identifier": "com.gac-info.app", "build": {"beforeDevCommand": "pnpm dev", "devUrl": "http://localhost:1420", "beforeBuildCommand": "pnpm build", "frontendDist": "../dist"}, "app": {"windows": [{"title": "GAC Info", "width": 400, "height": 300, "resizable": false, "visible": false, "decorations": true, "center": true}], "security": {"csp": null}, "trayIcon": {"iconPath": "icons/icon.png", "iconAsTemplate": true, "menuOnLeftClick": false}}, "bundle": {"active": true, "targets": "all", "icon": ["icons/32x32.png", "icons/128x128.png", "icons/<EMAIL>", "icons/icon.icns", "icons/icon.ico"]}}